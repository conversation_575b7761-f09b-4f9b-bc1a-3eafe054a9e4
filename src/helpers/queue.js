export async function sendToEmailQueue(env, data, options = {}) {
  return env.EMAIL_QUEUE.send({ type: 'email', ...data }, options);
}

export async function sendToSalesforceQueue(env, data, options = {}) {
  return env.SALESFORCE_QUEUE.send({ type: 'salesforce', ...data }, options);
}

export async function sendToAdminQueue(env, data, options = {}) {
  return env.ADMIN_QUEUE.send({ type: 'admin', ...data }, options);
}
