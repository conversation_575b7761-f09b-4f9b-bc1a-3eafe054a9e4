import { createFactory } from 'hono/factory';
import { zValidator } from '@hono/zod-validator';
import { AppError, cleanedApplication, updateApplicationInD1, extractAndSanitizePIIFields, putAppPII } from '../../helpers';
import { z } from 'zod';
import { getMeta } from '../../utils';
import { ensureApplicationByUUID } from './get-app';
import { applicationSchema } from '../../schema/application';
import { createEmbeddedSession, sendPandaDocSilently, updatePandaDocFields, createPandaDocSession } from '../../pandadoc';

const factory = createFactory();

const validator = zValidator('json', z.object({ applicationFields: applicationSchema }), async (result) => {
  if (!result.success) {
    console.log(JSON.stringify(result.error.issues, null, 2));
    throw new AppError('Validation Error: Invalid applicationFields', 400, 'validationError', result.error);
  }
});

export const submitAppHandlers = factory.createHandlers(validator, ensureApplicationByUUID, async (c) => {
  const timestamp = c.get('timestamp');
  const uuid = c.req.param('uuid');

  const fetchApplication = c.get('application');
  const application = { ...fetchApplication };
  let applicationFields = c.req.valid('json').applicationFields;

  const { piiData, sanitizedApplicationFields } = extractAndSanitizePIIFields(applicationFields);

  application.meta.submitted = getMeta(c.req.raw, timestamp);
  application.submitted_at = timestamp;

  if (application.status === 'APP_EDITING') {
    application.applicationFields = {
      ...(application.applicationFields || {}),
      ...applicationFields,
    };

    await updatePandaDocFields(c.env, application);
    await sendPandaDocSilently(c.env, application.pandadoc.document.id);
    const session = await createEmbeddedSession(c.env, application.pandadoc.document.id, application);
    application.pandadoc.session = session;
  } else {
    application.applicationFields = applicationFields;
    if (!application.pandadoc) {
      const pandadoc = await createPandaDocSession(c.env, application);
      application.pandadoc = pandadoc;
    }
  }

  application.status = 'APP_SUBMITTED';
  applicationFields = sanitizedApplicationFields;

  await Promise.all([putAppPII(c.env, uuid, piiData), updateApplicationInD1(c.env, application.uuid, application, timestamp)]);

  return c.json({ data: cleanedApplication(application) });
});
