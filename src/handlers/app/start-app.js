import { createFactory } from 'hono/factory';
import { ensureApplicationByUUID } from './get-app';
import { AppError, cleanedApplication, updateApplicationInD1 } from '../../helpers';
import { getMeta } from '../../utils';

const factory = createFactory();

export const startAppHandlers = factory.createHandlers(ensureApplicationByUUID, async (c) => {
  const timestamp = c.get('timestamp');
  const application = c.get('application');

  if (!['PREQUAL_APPROVED', 'PREQUAL_FAST_TRACK'].includes(application.status)) {
    console.error(`Application status ${application.status} can't be started`);
    throw new AppError(`Application can't be started`, 400, 'startApp', `Status is ${application.status}`);
  }

  application.status = 'APP_STARTED';
  application.started_at = timestamp;

  if (!application.meta) {
    application.meta = {};
  }
  application.meta.started = getMeta(c.req.raw, timestamp);

  await updateApplicationInD1(c.env, application.uuid, application, timestamp);

  return c.json({ data: cleanedApplication(application) });
});
