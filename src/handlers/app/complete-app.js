import { createFactory } from 'hono/factory';
import { zValidator } from '@hono/zod-validator';
import { AppError, cleanedApplication, updateApplicationInD1, extractAndSanitizePIIFields, putAppBankStatements } from '../../helpers';
import { getMeta } from '../../utils';
import { ensureApplicationByUUID } from './get-app';
import { bankStatementsSchema } from '../../schema/application';
import { sendToEmailQueue, sendToSalesforceQueue, sendToAdminQueue } from '../../helpers/queue';

const factory = createFactory();

const validator = zValidator('json', bankStatementsSchema, async (result) => {
  if (!result.success) {
    throw new AppError('Validation Error: Invalid applicationFields', 400, 'validationError', result.error);
  }
});

export const completeAppHandlers = factory.createHandlers(validator, ensureApplicationByUUID, async (c) => {
  const uuid = c.req.param('uuid');
  const timestamp = c.get('timestamp');
  const application = c.get('application');
  let bankStatements = c.req.valid('json').bankStatements;

  console.log('Uploaded', bankStatements.length, 'bank statements');

  // Verify the application is in a state that can be completed
  if (application.status !== 'APP_SIGNED') {
    console.error(`Application status ${application.status} can't be completed`);
    throw new AppError(`Application can't be completed`, 400, 'completeApp', `Status is ${application.status}`);
  }

  const { sanitizedApplicationFields } = extractAndSanitizePIIFields(application.applicationFields);

  // Update application fields
  application.applicationFields = sanitizedApplicationFields;

  // Update status and timestamps
  application.completed_at = timestamp;
  application.status = 'APP_COMPLETED';
  application.meta.completed = getMeta(c.req.raw, timestamp);

  await Promise.all([
    bankStatements.length > 0 && putAppBankStatements(c.env, uuid, bankStatements),
    updateApplicationInD1(c.env, uuid, application, timestamp),
    sendToAdminQueue(c.env, { application: { uuid: application.uuid, status: application.status } }),
    // Uncomment if needed:
    // sendToEmailQueue(c.env, { application }),
    // sendToSalesforceQueue(c.env, { application }),
  ]);

  return c.json({ data: cleanedApplication(application) });
});
