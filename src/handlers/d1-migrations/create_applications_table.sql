DROP TABLE IF EXISTS applications;

CREATE TABLE IF NOT EXISTS applications (
  uuid TEXT PRIMARY KEY,
  version INTEGER NOT NULL,
  status TEXT NOT NULL,
  domain TEXT NOT NULL,
  preQualifyFields TEXT NOT NULL,       -- <PERSON><PERSON><PERSON> stored as TEXT
  approvalAmount INTEGER DEFAULT 0,
  agent TEXT,                           -- <PERSON><PERSON><PERSON> stored as TEXT
  reason TEXT,
  applicationFields TEXT,               -- <PERSON><PERSON><PERSON> stored as TEXT
  pandadoc TEXT,                        -- <PERSON><PERSON><PERSON> stored as TEXT
  utm TEXT,                             -- <PERSON><PERSON><PERSON> stored as TEXT
  meta TEXT,                            -- <PERSON><PERSON><PERSON> stored as TEXT
  fastTrack INTEGER NOT NULL DEFAULT 0, -- BOOLEAN as INTEGER (0/1)
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  started_at TEXT,
  submitted_at TEXT,
  signed_at TEXT,
  completed_at TEXT
);

-- Trigger to auto-update updated_at
CREATE TRIGGER update_applications_updated_at
AFTER UPDATE ON applications
FOR EACH ROW
BEGIN
  UPDATE applications SET updated_at = datetime('now') WHERE uuid = OLD.uuid;
END;


