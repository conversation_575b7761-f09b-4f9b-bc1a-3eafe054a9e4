import { createFactory } from 'hono/factory';
import { extractAndSanitizePIIFields } from '../../helpers';
import { getAllApplicationsFromD1 } from '../../helpers/d1';

const factory = createFactory();

const DRY_RUN = false;

export const d1MigrationHandlers = factory.createHandlers(async (c) => {
  const env = c.env;
  const applications = await getAllApplicationsFromKV(env);
  const count = applications.length;
  console.log('Found', count, 'applications to migrate');

  const migratedApplications = await migrateApplications(env, applications);

  return c.json({ data: { count, migratedApplications } });
});

async function getAllApplicationsFromKV(env) {
  const keys = await env.KV.list({ prefix: 'app:' });

  const appKeys = keys.keys.filter((key) => {
    const [_, uuid, subKey] = key.name.split(':');
    return !subKey;
  });

  const applications = await Promise.all(
    appKeys.map(async (key, index) => {
      const application = await env.KV.get(key.name, 'json');
      if (application.applicationFields) {
        const { sanitizedApplicationFields } = extractAndSanitizePIIFields(application.applicationFields);
        application.applicationFields = sanitizedApplicationFields;
      }
      return application;
    })
  );
  return applications;
}

function makeSqlInsertBatch(env, applications, tableName, jsonCols = [], skipCols = []) {
  const appKeys = [
    'uuid',
    'version',
    'status',
    'domain',
    'preQualifyFields',
    'approvalAmount',
    'agent',
    'reason',
    'applicationFields',
    'pandadoc',
    'utm',
    'meta',
    'fastTrack',
    'created_at',
    'updated_at',
    'started_at',
    'submitted_at',
    'signed_at',
    'completed_at',
  ];

  const insertStatement = `INSERT INTO ${tableName} (${appKeys.join(',')}) VALUES (${appKeys.map(() => '?').join(',')})`;

  const statemts = [];

  applications.forEach((app) => {
    const rowValues = appKeys.map((key) => {
      const val = app[key];

      if (key == 'domain' && !app.domain) {
        return 'app.pinnaclefunding.com';
      }

      //migrate old fields key name to preQualifyFields
      if (key == 'preQualifyFields' && !app.preQualifyFields) {
        return `${JSON.stringify(app.fields)}`;
      }

      // set default fastTrack to false
      if (key == 'fastTrack' && app.fastTrack == undefined) {
        return 0;
      }

      if (jsonCols.includes(key)) {
        if (!val) return 'NULL';

        return `${JSON.stringify(val)}`;
      }

      if (val === null || val === '' || val === undefined || skipCols.includes(key)) {
        return 'NULL';
      }

      return `${String(val).replace(/'/g, '').replace(/"/g, "'")}`;
    });

    const statement = env.DB.prepare(insertStatement).bind(...rowValues);
    statemts.push(statement);
  });

  return statemts;
}

async function migrateApplications(env, applications) {
  const statements = makeSqlInsertBatch(env, applications, 'applications', [
    'preQualifyFields',
    'applicationFields',
    'pandadoc',
    'utm',
    'meta',
    'agent',
  ]);

  if (DRY_RUN) {
    console.log(statements);
    return;
  }

  await env.DB.batch(statements);

  return await getAllApplicationsFromD1(env);
}
