import { describe, it, expect, beforeEach } from 'vitest';
import { env } from 'cloudflare:test';
import { getApplicationFromD1, updateApplicationInD1 } from '../../src/helpers/d1';
import { generateApplicationData, generatePrequalData } from '../setup';
import { setupD1Database, clearD1Database } from './d1-setup';

describe('D1 Helper Functions', () => {
  beforeEach(async () => {
    // Setup D1 database schema
    await setupD1Database(env);

    // Clear D1 database before each test
    await clearD1Database(env);

    // Set version for tests
    env.VERSION = '1.0.0';
  });

  describe('updateApplicationInD1', () => {
    it('creates a new application in D1', async () => {
      const uuid = 'test-uuid-1';
      const applicationData = {
        uuid,
        version: env.VERSION,
        status: 'PREQUAL_APPROVED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        agent: {
          name: 'Test Agent',
          email: '<EMAIL>',
        },
        utm: {
          utm_source: 'test',
          utm_medium: 'test',
        },
        meta: {
          initiated: {
            timestamp: new Date().toISOString(),
          },
        },
        fastTrack: false,
        created_at: new Date().toISOString(),
      };

      const result = await updateApplicationInD1(env, uuid, applicationData);

      expect(result).toEqual(
        expect.objectContaining({
          uuid,
          status: 'PREQUAL_APPROVED',
          domain: 'app.pinnaclefunding.com',
          fastTrack: false,
        })
      );
      expect(result.updated_at).toBeDefined();
    });

    it('updates an existing application in D1', async () => {
      const uuid = 'test-uuid-2';
      const initialData = {
        uuid,
        version: env.VERSION,
        status: 'PREQUAL_APPROVED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        fastTrack: false,
        created_at: new Date().toISOString(),
      };

      // Create initial application
      await updateApplicationInD1(env, uuid, initialData);

      // Update the application
      const updatedData = {
        ...initialData,
        status: 'APP_STARTED',
        started_at: new Date().toISOString(),
      };

      const result = await updateApplicationInD1(env, uuid, updatedData);

      expect(result.status).toBe('APP_STARTED');
      expect(result.started_at).toBeDefined();
    });

    it('handles JSON fields correctly', async () => {
      const uuid = 'test-uuid-3';
      const applicationData = {
        uuid,
        version: env.VERSION,
        status: 'APP_SUBMITTED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        applicationFields: generateApplicationData(),
        agent: {
          name: 'Test Agent',
          email: '<EMAIL>',
        },
        pandadoc: {
          document: {
            id: 'test-doc-id',
          },
        },
        fastTrack: true,
        created_at: new Date().toISOString(),
      };

      await updateApplicationInD1(env, uuid, applicationData);
      const retrieved = await getApplicationFromD1(env, uuid);

      expect(retrieved.preQualifyFields).toEqual(applicationData.preQualifyFields);
      expect(retrieved.applicationFields).toEqual(applicationData.applicationFields);
      expect(retrieved.agent).toEqual(applicationData.agent);
      expect(retrieved.pandadoc).toEqual(applicationData.pandadoc);
      expect(retrieved.fastTrack).toBe(true);
    });
  });

  describe('getApplicationFromD1', () => {
    it('retrieves an application from D1', async () => {
      const uuid = 'test-uuid-4';
      const applicationData = {
        uuid,
        version: env.VERSION,
        status: 'PREQUAL_APPROVED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        fastTrack: false,
        created_at: new Date().toISOString(),
      };

      await updateApplicationInD1(env, uuid, applicationData);
      const retrieved = await getApplicationFromD1(env, uuid);

      expect(retrieved).toEqual(
        expect.objectContaining({
          uuid,
          status: 'PREQUAL_APPROVED',
          domain: 'app.pinnaclefunding.com',
          fastTrack: false,
        })
      );
      expect(retrieved.preQualifyFields).toEqual(applicationData.preQualifyFields);
    });

    it('returns null for non-existent application', async () => {
      const result = await getApplicationFromD1(env, 'non-existent-uuid');
      expect(result).toBeNull();
    });

    it('returns null for empty uuid', async () => {
      const result = await getApplicationFromD1(env, '');
      expect(result).toBeNull();
    });

    it('handles boolean conversion for fastTrack correctly', async () => {
      const uuid1 = 'test-uuid-5';
      const uuid2 = 'test-uuid-6';

      // Test fastTrack: true
      await updateApplicationInD1(env, uuid1, {
        uuid: uuid1,
        version: env.VERSION,
        status: 'PREQUAL_FAST_TRACK',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        fastTrack: true,
        created_at: new Date().toISOString(),
      });

      // Test fastTrack: false
      await updateApplicationInD1(env, uuid2, {
        uuid: uuid2,
        version: env.VERSION,
        status: 'PREQUAL_APPROVED',
        domain: 'app.pinnaclefunding.com',
        preQualifyFields: generatePrequalData(),
        fastTrack: false,
        created_at: new Date().toISOString(),
      });

      const app1 = await getApplicationFromD1(env, uuid1);
      const app2 = await getApplicationFromD1(env, uuid2);

      expect(app1.fastTrack).toBe(true);
      expect(app2.fastTrack).toBe(false);
    });
  });
});
