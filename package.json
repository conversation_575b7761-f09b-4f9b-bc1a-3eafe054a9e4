{"name": "app-sync-worker", "version": "4.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "deploy:dev": "wrangler deploy -e=dev", "dev": "wrangler dev -e=dev", "start": "wrangler dev", "test": "vitest run"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.7.8", "vitest": "~3.0.9", "wrangler": "^4.19.1"}, "dependencies": {"@hono/zod-validator": "^0.4.3", "hono": "^4.7.11", "ua-parser-js": "^2.0.3", "zod": "^3.25.51", "pdf-lib": "^1.17.1"}}